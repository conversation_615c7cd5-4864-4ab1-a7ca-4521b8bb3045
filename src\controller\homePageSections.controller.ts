import { Request, Response } from "express";
import HomePageSectionsService from "../services/homePageSections.service";
import { IHomePageSections } from "../models/HomePageSections.model";

export interface IHomePageSectionsController {
  getAllSections(req: Request, res: Response): Promise<void>;
  getSectionByType(req: Request, res: Response): Promise<void>;
  updateSectionByType(req: Request, res: Response): Promise<void>;
  getVisibleSections(req: Request, res: Response): Promise<void>;
  getVisibleSectionByType(req: Request, res: Response): Promise<void>;
}

class HomePageSectionsController implements IHomePageSectionsController {
  async getAllSections(req: Request, res: Response): Promise<void> {
    try {
      const sections = await HomePageSectionsService.getAllSections();

      res.status(200).json({
        success: true,
        data: sections,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch sections",
      });
    }
  }

  async getSectionByType(req: Request, res: Response): Promise<void> {
    try {
      const { sectionType } = req.params;
      const section = await HomePageSectionsService.getSectionByType(
        sectionType
      );

      res.status(200).json({
        success: true,
        data: section,
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        message: error.message || "Section not found",
      });
    }
  }

  async updateSectionByType(req: Request, res: Response): Promise<void> {
    try {
      const { sectionType } = req.params;
      const updateData: Partial<IHomePageSections> = req.body;
      const section = await HomePageSectionsService.updateSectionByType(
        sectionType,
        updateData
      );

      res.status(200).json({
        success: true,
        message: "Section updated successfully",
        data: section,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || "Failed to update section",
      });
    }
  }

  async getVisibleSections(req: Request, res: Response): Promise<void> {
    try {
      const sections = await HomePageSectionsService.getVisibleSections();

      res.status(200).json({
        success: true,
        data: sections,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch visible sections",
      });
    }
  }

  async getVisibleSectionByType(req: Request, res: Response): Promise<void> {
    try {
      const { sectionType } = req.params;
      const section = await HomePageSectionsService.getVisibleSectionByType(
        sectionType
      );

      res.status(200).json({
        success: true,
        data: section,
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        message: error.message || "Section not found or not visible",
      });
    }
  }
}

export default new HomePageSectionsController();

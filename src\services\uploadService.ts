import {
  s3Client,
  s3UploadParams,
  onboardingS3UploadParams,
} from "../config/s3";

export class FileUploadService {
  static async uploadFile(file: any, documentName: any) {
    let uploadParams = s3UploadParams;
    uploadParams.Body = file;
    uploadParams.Key = String(documentName);

    let uploadConfirm = await s3Client.upload(uploadParams).promise();
    return uploadConfirm;
  }

  static async getBucket() {
    s3Client.listBuckets((err: any, data: any) => {
      if (err) console.error(err);
      console.log(data);
    });
  }

  static async uploadBase64(
    buffer: Buffer,
    fileName: string,
    mimeType: string
  ) {
    let uploadParams = s3UploadParams;
    uploadParams.Body = buffer;
    uploadParams.Key = fileName;
    uploadParams.ContentType = mimeType;
    uploadParams.ContentEncoding = "base64";

    let uploadConfirm = await s3Client.upload(uploadParams).promise();
    return uploadConfirm;
  }

  static async onboardingUploadFile(file: any, documentName: any) {
    let uploadParams = onboardingS3UploadParams;
    uploadParams.Body = file;
    uploadParams.Key = String(documentName);

    let uploadConfirm = await s3Client.upload(uploadParams).promise();
    return uploadConfirm;
  }

  // CMS Upload methods for blog images
  static async uploadCMSFile(file: any, fileName: string, folder: string = "general") {
    let uploadParams = s3UploadParams;
    uploadParams.Body = file;
    uploadParams.Key = `CMS/${folder}/${fileName}`;

    let uploadConfirm = await s3Client.upload(uploadParams).promise();
    return uploadConfirm;
  }

  static async deleteCMSFile(fileName: string, folder: string = "general") {
    const deleteParams = {
      Bucket: s3UploadParams.Bucket,
      Key: `CMS/${folder}/${fileName}`
    };

    try {
      await s3Client.deleteObject(deleteParams).promise();
      return true;
    } catch (error) {
      console.error("Error deleting file from S3:", error);
      return false;
    }
  }
}

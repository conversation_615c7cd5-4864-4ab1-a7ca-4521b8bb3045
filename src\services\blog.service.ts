import { IBlog } from "../models/blogs.model";
import BlogD<PERSON> from "../lib/dao/blog.dao";

export interface IBlogService {
  createBlog(blogData: Partial<IBlog>): Promise<IBlog>;
  getBlogById(id: string): Promise<IBlog>;
  getBlogByHandle(handle: string): Promise<IBlog>;
  getAllBlogs(query?: any): Promise<IBlog[]>;
  updateBlog(id: string, updateData: Partial<IBlog>): Promise<IBlog>;
  updateBlogByHandle(
    handle: string,
    updateData: Partial<IBlog>
  ): Promise<IBlog>;
  deleteBlog(id: string): Promise<boolean>;
  deleteBlogByHandle(handle: string): Promise<boolean>;
  getVisibleBlogs(): Promise<IBlog[]>;
}

class BlogService implements IBlogService {
  async createBlog(blogData: Partial<IBlog>): Promise<IBlog> {
    // Business logic validation
    if (!blogData.blogName) {
      throw new Error("Blog name is required");
    }

    // Check if handle already exists
    if (blogData.handle) {
      const existingBlog = await BlogDao.findByHandle(blogData.handle);
      if (existingBlog) {
        throw new Error("Blog handle already exists");
      }
    }

    return await BlogDao.create(blogData);
  }

  async getBlogById(id: string): Promise<IBlog> {
    const blog = await BlogDao.findById(id);
    if (!blog) {
      throw new Error("Blog not found");
    }
    return blog;
  }

  async getBlogByHandle(handle: string): Promise<IBlog> {
    const blog = await BlogDao.findByHandle(handle);
    if (!blog) {
      throw new Error("Blog not found");
    }
    return blog;
  }

  async getAllBlogs(query: any = {}): Promise<IBlog[]> {
    return await BlogDao.findAll(query);
  }

  async updateBlog(id: string, updateData: Partial<IBlog>): Promise<IBlog> {
    // Check if blog exists
    const existingBlog = await BlogDao.findById(id);
    if (!existingBlog) {
      throw new Error("Blog not found");
    }

    // Check if new handle conflicts with existing blogs
    if (updateData.handle && updateData.handle !== existingBlog.handle) {
      const handleExists = await BlogDao.findByHandle(updateData.handle);
      if (handleExists) {
        throw new Error("Blog handle already exists");
      }
    }

    const updatedBlog = await BlogDao.update(id, updateData);
    if (!updatedBlog) {
      throw new Error("Failed to update blog");
    }
    return updatedBlog;
  }

  async updateBlogByHandle(
    handle: string,
    updateData: Partial<IBlog>
  ): Promise<IBlog> {
    // Check if blog exists
    const existingBlog = await BlogDao.findByHandle(handle);
    if (!existingBlog) {
      throw new Error("Blog not found");
    }

    // Check if new handle conflicts with existing blogs
    if (updateData.handle && updateData.handle !== handle) {
      const handleExists = await BlogDao.findByHandle(updateData.handle);
      if (handleExists) {
        throw new Error("Blog handle already exists");
      }
    }

    const updatedBlog = await BlogDao.updateByHandle(handle, updateData);
    if (!updatedBlog) {
      throw new Error("Failed to update blog");
    }
    return updatedBlog;
  }

  async deleteBlog(id: string): Promise<boolean> {
    const existingBlog = await BlogDao.findById(id);
    if (!existingBlog) {
      throw new Error("Blog not found");
    }

    return await BlogDao.delete(id);
  }

  async deleteBlogByHandle(handle: string): Promise<boolean> {
    const existingBlog = await BlogDao.findByHandle(handle);
    if (!existingBlog) {
      throw new Error("Blog not found");
    }

    return await BlogDao.deleteByHandle(handle);
  }

  async getVisibleBlogs(): Promise<IBlog[]> {
    return await BlogDao.findVisible();
  }
}

export default new BlogService();

import { Schema } from "mongoose";
import BlogModel, { IBlog } from "../../models/blogs.model";

export interface IBlogDao {
  create(blogData: Partial<IBlog>): Promise<IBlog>;
  findById(id: string): Promise<IBlog | null>;
  findByHandle(handle: string): Promise<IBlog | null>;
  findAll(query?: any): Promise<IBlog[]>;
  update(id: string, updateData: Partial<IBlog>): Promise<IBlog | null>;
  updateByHandle(
    handle: string,
    updateData: Partial<IBlog>
  ): Promise<IBlog | null>;
  delete(id: string): Promise<boolean>;
  deleteByHandle(handle: string): Promise<boolean>;
  findVisible(): Promise<IBlog[]>;
}

class BlogDao implements IBlogDao {
  async create(blogData: Partial<IBlog>): Promise<IBlog> {
    const blog = new BlogModel(blogData);
    return await blog.save();
  }

  async findById(id: string): Promise<IBlog | null> {
    return await BlogModel.findById(id);
  }

  async findByHandle(handle: string): Promise<IBlog | null> {
    return await BlogModel.findOne({ handle });
  }

  async findAll(query: any = {}): Promise<IBlog[]> {
    return await BlogModel.find(query).sort({ createdAt: -1 });
  }

  async update(id: string, updateData: Partial<IBlog>): Promise<IBlog | null> {
    return await BlogModel.findByIdAndUpdate(id, updateData, { new: true });
  }

  async updateByHandle(
    handle: string,
    updateData: Partial<IBlog>
  ): Promise<IBlog | null> {
    return await BlogModel.findOneAndUpdate({ handle }, updateData, {
      new: true,
    });
  }

  async delete(id: string): Promise<boolean> {
    const result = await BlogModel.findByIdAndDelete(id);
    return !!result;
  }

  async deleteByHandle(handle: string): Promise<boolean> {
    const result = await BlogModel.findOneAndDelete({ handle });
    return !!result;
  }

  async findVisible(): Promise<IBlog[]> {
    return await BlogModel.find({ isVisible: true }).sort({ createdAt: -1 });
  }
}

export default new BlogDao();

import { Schema } from "mongoose";
import HomePageSection, {
  IHomePageSections,
  IJournalSection,
} from "../../models/HomePageSections.model";

export const HOME_PAGE_SECTIONS = [
  {
    sectionType: "journal",
    sectionTitle: "Journal Section",
    arrayField: "journals",
  },
];

export interface IHomePageSectionsDao {
  findAll(): Promise<IHomePageSections[]>;
  findBySectionType(sectionType: string): Promise<IHomePageSections | null>;
  updateBySectionType(
    sectionType: string,
    updateData: Partial<IHomePageSections>
  ): Promise<IHomePageSections | null>;
  getVisibleSections(): Promise<IHomePageSections[]>;
  getVisibleSectionByType(
    sectionType: string
  ): Promise<IHomePageSections | null>;
  createSection(
    sectionData: Partial<IHomePageSections>
  ): Promise<IHomePageSections>;
}

class HomePageSectionsDao implements IHomePageSectionsDao {
  async findAll(): Promise<IHomePageSections[]> {
    return await HomePageSection.find().sort({ createdAt: -1 });
  }

  async findBySectionType(
    sectionType: string
  ): Promise<IHomePageSections | null> {
    return await HomePageSection.findOne({ sectionType });
  }

  async updateBySectionType(
    sectionType: string,
    updateData: Partial<IHomePageSections>
  ): Promise<IHomePageSections | null> {
    return await HomePageSection.findOneAndUpdate({ sectionType }, updateData, {
      new: true,
    });
  }

  async getVisibleSections(): Promise<IHomePageSections[]> {
    return await HomePageSection.find({ isVisible: true }).sort({
      createdAt: -1,
    });
  }

  async getVisibleSectionByType(
    sectionType: string
  ): Promise<IHomePageSections | null> {
    return await HomePageSection.findOne({ sectionType, isVisible: true });
  }

  async createSection(
    sectionData: Partial<IHomePageSections>
  ): Promise<IHomePageSections> {
    return await HomePageSection.create(sectionData);
  }
}

export default new HomePageSectionsDao();

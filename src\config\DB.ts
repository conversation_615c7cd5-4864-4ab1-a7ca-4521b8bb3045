import dotenv from "dotenv";
import mongoose from "mongoose";
import { IServer } from "../lib/interfaces/IServer";
import { CONFIG } from "./environment";
import HomePageSectionsService from "../services/homePageSections.service";

dotenv.config();

export class DB {
  static async connect(server?: IServer) {
    try {
      console.log("Connecting to DB");
      await mongoose.connect(CONFIG.DB_CONNECTION_STRING!);
      if (server) {
        server.isDbConnected = true;
      }
      console.log("Connected to <PERSON>");

      // Initialize Home Page Sections
      await HomePageSectionsService.initializeHomePageSections();
    } catch (error) {
      throw error;
    }
  }
}

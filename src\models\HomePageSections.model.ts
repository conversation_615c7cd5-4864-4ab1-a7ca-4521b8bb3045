/* Home Page Sections included in this schema:
Journal
 */
import { Schema, model, Document } from "mongoose";

export interface IHomePageSections extends Document {
  sectionType: string;
  sectionTitle?: string;
  sectionSubtitle?: string;
  isVisible?: boolean;
  image?: string;
  heading?: string;
  subHeading?: string;
  buttonText?: string;
  link?: string;
  DesktopBannerImage?: string;
  MobileBannerImage?: string;
  question?: string;
  answer?: string;
}

// Journal Section Interface (for blogs)
export interface IJournalSection extends IHomePageSections {
  journals: {
    blogID?: Schema.Types.ObjectId;
    position?: number;
  }[];
}

// Journals Schema
const journalsSchema = new Schema({
  blogID: { type: Schema.Types.ObjectId, ref: "blog" },
  position: { type: Number },
});

// Base HomePageSections Schema
const HomePageSectionsSchema = new Schema<IHomePageSections>(
  {
    sectionType: {
      type: String,
      required: true,
      index: true,
    },
    sectionTitle: { type: String, sparse: true },
    sectionSubtitle: { type: String, sparse: true },
    isVisible: { type: Boolean },
    image: { type: String, sparse: true },
    heading: { type: String, sparse: true },
    subHeading: { type: String, sparse: true },
    buttonText: { type: String, sparse: true },
    link: { type: String, sparse: true },
    DesktopBannerImage: { type: String, sparse: true },
    MobileBannerImage: { type: String, sparse: true },
    question: { type: String, sparse: true },
    answer: { type: String, sparse: true },
  },
  {
    timestamps: true,
    discriminatorKey: "sectionType", // Use sectionType to determine document structure
  }
);

// Base HomePageSection model
const HomePageSection = model<IHomePageSections>(
  "homepageSection",
  HomePageSectionsSchema
);

// Journal discriminator
HomePageSection.discriminator<IJournalSection>(
  "journal",
  new Schema({
    journals: { type: [journalsSchema], default: [] },
  })
);

export default HomePageSection;

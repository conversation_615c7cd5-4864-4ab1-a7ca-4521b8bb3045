import { Schema, model, Document } from "mongoose";

export interface IBlog extends Document {
  handle: string;
  isVisible?: boolean;
  blogName?: string;
  publishDate?: string;
  tag: string[];
  description?: string;
  featureImage?: string;
  writerName?: string;
  writerShortname?: string;
  writerImage?: string;
  writerDesignation?: string;
}

const generateHandle = async (blogName: string): Promise<string> => {
  let handle = blogName
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");

  let exists = await model<IBlog>("blog").findOne({ handle });
  let counter = 1;

  while (exists) {
    const newHandle = `${handle}-${counter}`;
    exists = await model<IBlog>("blog").findOne({ handle: newHandle });
    if (!exists) {
      handle = newHandle;
      break;
    }
    counter++;
  }
  return handle;
};

const BlogsSchema = new Schema<IBlog>(
  {
    handle: { type: String, unique: true },
    isVisible: { type: Boolean },
    blogName: { type: String },
    publishDate: { type: String },
    tag: { type: [String], default: [] },
    description: { type: String },
    featureImage: { type: String },
    writerName: { type: String },
    writerShortname: { type: String },
    writerImage: { type: String },
    writerDesignation: { type: String },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

BlogsSchema.pre("save", async function (next) {
  if (this.isNew && !this.handle) {
    this.handle = await generateHandle(this.blogName || "");
  }
  next();
});

export default model<IBlog>("blog", BlogsSchema);

import {
  IHomePageSections,
  IJournalSection,
} from "../models/HomePageSections.model";
import HomePageSectionsDao from "../lib/dao/homePageSections.dao";
import BlogDao from "../lib/dao/blog.dao";
import { HOME_PAGE_SECTIONS } from "../lib/enum/homePageSections.enum";

export interface IHomePageSectionsService {
  getAllSections(): Promise<IHomePageSections[]>;
  getSectionByType(sectionType: string): Promise<IHomePageSections>;
  updateSectionByType(
    sectionType: string,
    updateData: Partial<IHomePageSections>
  ): Promise<IHomePageSections>;
  getVisibleSections(): Promise<IHomePageSections[]>;
  getVisibleSectionByType(sectionType: string): Promise<any>;
  initializeHomePageSections(): Promise<void>;
}

class HomePageSectionsService implements IHomePageSectionsService {
  async getAllSections(): Promise<IHomePageSections[]> {
    return await HomePageSectionsDao.findAll();
  }

  async getSectionByType(sectionType: string): Promise<IHomePageSections> {
    const section = await HomePageSectionsDao.findBySectionType(sectionType);
    if (!section) {
      throw new Error("Section not found");
    }
    return section;
  }

  async updateSectionByType(
    sectionType: string,
    updateData: Partial<IHomePageSections>
  ): Promise<IHomePageSections> {
    const existingSection = await HomePageSectionsDao.findBySectionType(
      sectionType
    );
    if (!existingSection) {
      throw new Error("Section not found");
    }

    const updatedSection = await HomePageSectionsDao.updateBySectionType(
      sectionType,
      updateData
    );
    if (!updatedSection) {
      throw new Error("Failed to update section");
    }
    return updatedSection;
  }

  async getVisibleSections(): Promise<IHomePageSections[]> {
    return await HomePageSectionsDao.getVisibleSections();
  }

  async getVisibleSectionByType(sectionType: string): Promise<any> {
    const section = await HomePageSectionsDao.getVisibleSectionByType(
      sectionType
    );
    if (!section) {
      throw new Error("Section not found or not visible");
    }

    // Handle different section types
    if (sectionType === "journal") {
      const journalSection = section as IJournalSection;
      const populatedJournals = await Promise.all(
        journalSection.journals.map(async (journal) => {
          if (journal.blogID) {
            const blog = await BlogDao.findById(journal.blogID.toString());
            return {
              blogID: journal.blogID,
              position: journal.position,
              blog: blog,
            };
          }
          return {
            blogID: journal.blogID,
            position: journal.position,
          };
        })
      );

      return {
        ...section.toObject(),
        journals: populatedJournals,
      };
    }

    // For other section types, return as is
    return section;
  }

  async initializeHomePageSections(): Promise<void> {
    try {
      console.log("Initializing Home Page Sections...");

      await Promise.all(
        HOME_PAGE_SECTIONS.map(async (section) => {
          const existingSection = await HomePageSectionsDao.findBySectionType(
            section.sectionType
          );

          if (!existingSection) {
            const newSection: Partial<IHomePageSections> = {
              sectionType: section.sectionType,
              sectionTitle: section.sectionTitle,
              isVisible: false,
            };

            // Add empty array if section has an array field
            if (section.arrayField) {
              (newSection as any)[section.arrayField] = [];
            }

            await HomePageSectionsDao.createSection(newSection);
            console.log(`Created ${section.sectionType} section in Home Page`);
          }
        })
      );

      console.log("Home Page sections initialization completed");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      console.error(`Home Page Sections Initialization Error: ${errorMessage}`);
    }
  }
}

export default new HomePageSectionsService();

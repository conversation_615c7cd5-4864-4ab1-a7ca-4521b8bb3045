import { Router } from "express";
import HomePageSectionsController from "../../controller/homePageSections.controller";
import { authMiddleware } from "../../middleware/AuthMiddleware";

export default class HomePageSectionsRouter {
  public router: Router;

  constructor() {
    this.router = Router();
    this.routes();
  }

  public routes(): void {
    // Protected Routes (with authentication)
    this.router.get(
      "/",
      // authMiddleware(),
      HomePageSectionsController.getAllSections
    );
    this.router.get(
      "/type/:sectionType",
      // authMiddleware(),
      HomePageSectionsController.getSectionByType
    );
    this.router.put(
      "/type/:sectionType",
      // authMiddleware(),
      HomePageSectionsController.updateSectionByType
    );

    // Public Routes (without authentication)
    this.router.get("/public", HomePageSectionsController.getVisibleSections);
    this.router.get(
      "/public/:sectionType",
      HomePageSectionsController.getVisibleSectionByType
    );
  }
}

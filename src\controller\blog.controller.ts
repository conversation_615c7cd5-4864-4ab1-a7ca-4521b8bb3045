import { Request, Response } from "express";
import BlogService from "../services/blog.service";
import { IBlog } from "../models/blogs.model";
import { FileUploadService } from "../services/uploadService";
import { Response as CustomResponse } from "../util/response";
import fs from "fs";
import path from "path";

export interface IBlogController {
  createBlog(req: Request, res: Response): Promise<void>;
  getBlogById(req: Request, res: Response): Promise<void>;
  getBlogByHandle(req: Request, res: Response): Promise<void>;
  getAllBlogs(req: Request, res: Response): Promise<void>;
  updateBlog(req: Request, res: Response): Promise<void>;
  updateBlogByHandle(req: Request, res: Response): Promise<void>;
  deleteBlog(req: Request, res: Response): Promise<void>;
  deleteBlogByHandle(req: Request, res: Response): Promise<void>;
  getVisibleBlogs(req: Request, res: Response): Promise<void>;
  uploadBlogImage(req: Request, res: Response): Promise<void>;
  uploadWriterImage(req: Request, res: Response): Promise<void>;
  deleteBlogImage(req: Request, res: Response): Promise<void>;
}

class BlogController implements IBlogController {
  async createBlog(req: Request, res: Response): Promise<void> {
    try {
      const blogData: Partial<IBlog> = req.body;
      const blog = await BlogService.createBlog(blogData);

      res.status(201).json({
        success: true,
        message: "Blog created successfully",
        data: blog,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || "Failed to create blog",
      });
    }
  }

  async getBlogById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const blog = await BlogService.getBlogById(id);

      res.status(200).json({
        success: true,
        data: blog,
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        message: error.message || "Blog not found",
      });
    }
  }

  async getBlogByHandle(req: Request, res: Response): Promise<void> {
    try {
      const { handle } = req.params;
      const blog = await BlogService.getBlogByHandle(handle);

      res.status(200).json({
        success: true,
        data: blog,
      });
    } catch (error: any) {
      res.status(404).json({
        success: false,
        message: error.message || "Blog not found",
      });
    }
  }

  async getAllBlogs(req: Request, res: Response): Promise<void> {
    try {
      const query = req.query;
      const blogs = await BlogService.getAllBlogs(query);

      res.status(200).json({
        success: true,
        data: blogs,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch blogs",
      });
    }
  }

  async updateBlog(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: Partial<IBlog> = req.body;
      const blog = await BlogService.updateBlog(id, updateData);

      res.status(200).json({
        success: true,
        message: "Blog updated successfully",
        data: blog,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || "Failed to update blog",
      });
    }
  }

  async updateBlogByHandle(req: Request, res: Response): Promise<void> {
    try {
      const { handle } = req.params;
      const updateData: Partial<IBlog> = req.body;
      const blog = await BlogService.updateBlogByHandle(handle, updateData);

      res.status(200).json({
        success: true,
        message: "Blog updated successfully",
        data: blog,
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || "Failed to update blog",
      });
    }
  }

  async deleteBlog(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      await BlogService.deleteBlog(id);

      res.status(200).json({
        success: true,
        message: "Blog deleted successfully",
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || "Failed to delete blog",
      });
    }
  }

  async deleteBlogByHandle(req: Request, res: Response): Promise<void> {
    try {
      const { handle } = req.params;
      await BlogService.deleteBlogByHandle(handle);

      res.status(200).json({
        success: true,
        message: "Blog deleted successfully",
      });
    } catch (error: any) {
      res.status(400).json({
        success: false,
        message: error.message || "Failed to delete blog",
      });
    }
  }

  async getVisibleBlogs(req: Request, res: Response): Promise<void> {
    try {
      const blogs = await BlogService.getVisibleBlogs();

      res.status(200).json({
        success: true,
        data: blogs,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch visible blogs",
      });
    }
  }

  async uploadBlogImage(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
        return;
      }

      const file = req.file;

      // Validate file type
      const ext = path.extname(file.originalname).toLowerCase();
      const allowedExtensions = [".jpg", ".jpeg", ".png", ".webp"];

      if (!allowedExtensions.includes(ext)) {
        // Delete file from local storage
        fs.unlinkSync(file.path);
        res.status(400).json({
          success: false,
          message: "Invalid file type. Only JPG, JPEG, PNG, and WebP are allowed.",
        });
        return;
      }

      const blob = fs.readFileSync(file.path);
      const fileName = `blog_${Date.now()}_${file.originalname}`;

      const fileUpload = await FileUploadService.uploadCMSFile(blob, fileName, "blogs");

      // Delete file from local storage after upload
      fs.unlinkSync(file.path);

      if (!fileUpload) {
        res.status(500).json({
          success: false,
          message: "There was some problem uploading the file!",
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: "Blog image uploaded successfully",
        data: {
          url: fileUpload.Location,
          fileName: fileName,
        },
      });
    } catch (error: any) {
      // Clean up local file if it exists
      if (req.file?.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to upload blog image",
      });
    }
  }

  async uploadWriterImage(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
        return;
      }

      const file = req.file;

      // Validate file type
      const ext = path.extname(file.originalname).toLowerCase();
      const allowedExtensions = [".jpg", ".jpeg", ".png", ".webp"];

      if (!allowedExtensions.includes(ext)) {
        // Delete file from local storage
        fs.unlinkSync(file.path);
        res.status(400).json({
          success: false,
          message: "Invalid file type. Only JPG, JPEG, PNG, and WebP are allowed.",
        });
        return;
      }

      const blob = fs.readFileSync(file.path);
      const fileName = `writer_${Date.now()}_${file.originalname}`;

      const fileUpload = await FileUploadService.uploadCMSFile(blob, fileName, "writers");

      // Delete file from local storage after upload
      fs.unlinkSync(file.path);

      if (!fileUpload) {
        res.status(500).json({
          success: false,
          message: "There was some problem uploading the file!",
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: "Writer image uploaded successfully",
        data: {
          url: fileUpload.Location,
          fileName: fileName,
        },
      });
    } catch (error: any) {
      // Clean up local file if it exists
      if (req.file?.path && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to upload writer image",
      });
    }
  }

  async deleteBlogImage(req: Request, res: Response): Promise<void> {
    try {
      const { fileName, folder } = req.body;

      if (!fileName) {
        res.status(400).json({
          success: false,
          message: "File name is required",
        });
        return;
      }

      const validFolders = ["blogs", "writers"];
      const targetFolder = validFolders.includes(folder) ? folder : "blogs";

      const deleteResult = await FileUploadService.deleteCMSFile(fileName, targetFolder);

      if (!deleteResult) {
        res.status(500).json({
          success: false,
          message: "Failed to delete image from storage",
        });
        return;
      }

      res.status(200).json({
        success: true,
        message: "Image deleted successfully",
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: error.message || "Failed to delete image",
      });
    }
  }
}

export default new BlogController();
